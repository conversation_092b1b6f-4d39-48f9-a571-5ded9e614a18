<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业梯度评价指标体系</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.8em;
            font-weight: 600;
        }

        .main-content {
            padding: 0;
        }

        .tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .tab {
            padding: 18px 30px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 15px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            position: relative;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9ff;
        }

        .tab:hover {
            background: #f8f9ff;
            color: #667eea;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .config-section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            overflow: hidden;
        }

        .config-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .config-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }

        .config-title .icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .config-body {
            padding: 25px;
        }

        .indicator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .indicator-category {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .category-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .indicator-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .indicator-item:last-child {
            border-bottom: none;
        }

        .indicator-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
        }

        .indicator-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .indicator-input {
            width: 100px;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
        }

        .weight-control {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .weight-slider {
            width: 120px;
        }

        .weight-display {
            min-width: 40px;
            font-size: 13px;
            color: #6c757d;
        }

        .evaluation-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }

        .input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .input-field {
            display: flex;
            flex-direction: column;
        }

        .input-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }

        .input-field input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .result-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            border-left: 5px solid #4facfe;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .result-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .result-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .result-item .value {
            font-size: 1.5em;
            font-weight: 600;
            color: #4facfe;
        }

        .result-item .label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            min-width: 70px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .status-reviewing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .score-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            min-width: 50px;
            text-align: center;
        }

        .score-high {
            background: #28a745;
        }

        .score-medium {
            background: #ffc107;
            color: #333;
        }

        .score-low {
            background: #dc3545;
        }

        @media (max-width: 768px) {
            .indicator-grid {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                text-align: center;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .review-filters > div {
                flex-direction: column;
                align-items: flex-start !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业梯度评价指标体系</h1>
        </div>

        <div class="main-content">
            <div class="tabs">
                <button class="tab active" onclick="switchTab('version-management')">版本管理</button>
                <button class="tab" onclick="switchTab('review')">智能评审</button>
            </div>

            <!-- 版本管理页面 -->
            <div id="version-management" class="tab-content active">
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                        <div>
                            <h2 style="color: #333; margin: 0 0 5px 0;">评价体系版本管理</h2>
                            <p style="color: #666; margin: 0;">管理和配置雏鹰-瞪羚-独角兽梯度评价指标体系</p>
                        </div>
                        <button class="btn btn-primary" onclick="showCreateTemplate()">
                            <span style="margin-right: 5px;">➕</span>新增模板
                        </button>
                    </div>

                    <!-- 当前使用版本 -->
                    <div class="config-section">
                        <div class="config-header">
                            <div class="config-title">
                                <span class="icon">🎯</span>
                                当前使用版本
                            </div>
                        </div>
                        <div class="config-body">
                            <div id="current-version-info" style="background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); padding: 25px; border-radius: 12px; border-left: 4px solid #2196f3;">
                                <div style="display: flex; justify-content: space-between; align-items: start;">
                                    <div>
                                        <div style="font-size: 1.3em; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
                                            <span id="current-version-name">默认配置</span>
                                        </div>
                                        <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">
                                            创建时间: <span id="current-version-time">系统默认</span>
                                        </div>
                                        <div style="font-size: 0.9em; color: #666;">
                                            描述: <span id="current-version-desc">系统默认的评价指标配置</span>
                                        </div>
                                    </div>
                                    <button class="btn btn-primary" onclick="editCurrentConfig()" style="background: #1976d2;">
                                        编辑配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模板列表 -->
                    <div class="config-section">
                        <div class="config-header">
                            <div class="config-title">
                                <span class="icon">📋</span>
                                配置模板列表
                            </div>
                            <div style="font-size: 0.9em; color: #666;">
                                共 <span id="template-count">0</span> 个模板
                            </div>
                        </div>
                        <div class="config-body">
                            <div id="template-list" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; min-height: 300px;">
                                <div id="template-table" style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: left; font-weight: 600;">模板名称</th>
                                                <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">状态</th>
                                                <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">创建时间</th>
                                                <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="template-tbody">
                                            <tr>
                                                <td colspan="4" style="padding: 50px; text-align: center; color: #666;">
                                                    <div style="font-size: 3em; margin-bottom: 15px; opacity: 0.3;">📋</div>
                                                    <div style="font-size: 1.1em; margin-bottom: 10px;">暂无配置模板</div>
                                                    <div style="font-size: 0.9em; color: #999;">点击"新增模板"创建您的第一个配置模板</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评价指标配置页面（二级页面） -->
            <div id="config" class="tab-content">
                <!-- 配置页面头部 -->
                <div style="margin-bottom: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h2 style="margin: 0 0 8px 0; font-size: 1.6em;">评价指标配置</h2>
                            <p style="margin: 0; opacity: 0.9; font-size: 1em;">
                                当前编辑: <span id="editing-template-name">新模板</span>
                            </p>
                        </div>
                        <div>
                            <button class="btn" onclick="backToVersionManagement()" style="background: rgba(255,255,255,0.2); color: white; margin-right: 10px;">
                                ← 返回版本管理
                            </button>
                            <button class="btn" onclick="saveCurrentTemplate()" style="background: rgba(255,255,255,0.9); color: #667eea;">
                                保存配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 雏鹰企业配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <div class="config-title">
                            <span class="icon">🦅</span>
                            雏鹰企业评价指标
                        </div>
                        <button class="btn btn-sm" onclick="resetEagleConfig()">重置默认</button>
                    </div>
                    <div class="config-body">
                        <div class="indicator-grid">
                            <!-- 基础条件 -->
                            <div class="indicator-category">
                                <div class="category-title">基础条件</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">成立时间(年)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-age" value="3" min="0" max="10">
                                        <span style="font-size: 12px; color: #6c757d;">≤</span>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">注册资本(万元)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-capital" value="100" min="0">
                                        <span style="font-size: 12px; color: #6c757d;">≥</span>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">员工规模(人)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-employees-min" value="10" min="0">
                                        <span>-</span>
                                        <input type="number" class="indicator-input" id="eagle-employees-max" value="50" min="0">
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">年营业收入(万元)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-revenue-min" value="500" min="0">
                                        <span>-</span>
                                        <input type="number" class="indicator-input" id="eagle-revenue-max" value="2000" min="0">
                                    </div>
                                </div>
                            </div>

                            <!-- 成长性指标 -->
                            <div class="indicator-category">
                                <div class="category-title">成长性指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">营收增长率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-revenue-growth" value="30" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="25" id="eagle-revenue-growth-weight">
                                            <span class="weight-display" id="eagle-revenue-growth-weight-display">25%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">利润增长率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-profit-growth" value="20" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="20" id="eagle-profit-growth-weight">
                                            <span class="weight-display" id="eagle-profit-growth-weight-display">20%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">员工增长率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-employee-growth" value="15" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="15" id="eagle-employee-growth-weight">
                                            <span class="weight-display" id="eagle-employee-growth-weight-display">15%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 创新能力指标 -->
                            <div class="indicator-category">
                                <div class="category-title">创新能力指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">研发投入占比(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-rd-ratio" value="3" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="20" id="eagle-rd-ratio-weight">
                                            <span class="weight-display" id="eagle-rd-ratio-weight-display">20%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">专利申请数量(件)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-patents" value="2" min="0">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="10" id="eagle-patents-weight">
                                            <span class="weight-display" id="eagle-patents-weight-display">10%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">技术人员占比(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="eagle-tech-ratio" value="30" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="10" id="eagle-tech-ratio-weight">
                                            <span class="weight-display" id="eagle-tech-ratio-weight-display">10%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 瞪羚企业配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <div class="config-title">
                            <span class="icon">🦌</span>
                            瞪羚企业评价指标
                        </div>
                        <button class="btn btn-sm" onclick="resetGazelleConfig()">重置默认</button>
                    </div>
                    <div class="config-body">
                        <div class="indicator-grid">
                            <!-- 基础条件 -->
                            <div class="indicator-category">
                                <div class="category-title">基础条件</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">成立时间(年)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-age-min" value="3" min="0">
                                        <span>-</span>
                                        <input type="number" class="indicator-input" id="gazelle-age-max" value="10" min="0">
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">年营业收入(万元)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-revenue-min" value="2000" min="0">
                                        <span>-</span>
                                        <input type="number" class="indicator-input" id="gazelle-revenue-max" value="100000" min="0">
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">连续3年营收增长率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-continuous-growth" value="20" min="0" step="0.1">
                                        <span style="font-size: 12px; color: #6c757d;">≥</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 成长性指标 -->
                            <div class="indicator-category">
                                <div class="category-title">成长性指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">营收复合增长率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-compound-growth" value="25" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="30" id="gazelle-compound-growth-weight">
                                            <span class="weight-display" id="gazelle-compound-growth-weight-display">30%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">市场份额增长</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-market-growth" value="15" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="15" id="gazelle-market-growth-weight">
                                            <span class="weight-display" id="gazelle-market-growth-weight-display">15%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">业务扩张能力</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-expansion" value="70" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="15" id="gazelle-expansion-weight">
                                            <span class="weight-display" id="gazelle-expansion-weight-display">15%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 创新能力指标 -->
                            <div class="indicator-category">
                                <div class="category-title">创新能力指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">研发投入占比(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-rd-ratio" value="5" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="25" id="gazelle-rd-ratio-weight">
                                            <span class="weight-display" id="gazelle-rd-ratio-weight-display">25%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">核心技术专利(件)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-core-patents" value="10" min="0">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="10" id="gazelle-core-patents-weight">
                                            <span class="weight-display" id="gazelle-core-patents-weight-display">10%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">产品创新度评分</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="gazelle-innovation-score" value="80" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="5" id="gazelle-innovation-score-weight">
                                            <span class="weight-display" id="gazelle-innovation-score-weight-display">5%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 独角兽企业配置 -->
                <div class="config-section">
                    <div class="config-header">
                        <div class="config-title">
                            <span class="icon">🦄</span>
                            独角兽企业评价指标
                        </div>
                        <button class="btn btn-sm" onclick="resetUnicornConfig()">重置默认</button>
                    </div>
                    <div class="config-body">
                        <div class="indicator-grid">
                            <!-- 基础条件 -->
                            <div class="indicator-category">
                                <div class="category-title">基础条件</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">企业估值(亿美元)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-valuation" value="10" min="0" step="0.1">
                                        <span style="font-size: 12px; color: #6c757d;">≥</span>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">成立时间(年)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-age" value="10" min="0">
                                        <span style="font-size: 12px; color: #6c757d;">≤</span>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">获得私募股权投资</span>
                                    <div class="indicator-controls">
                                        <select class="indicator-input" id="unicorn-investment" style="width: 120px;">
                                            <option value="true">是</option>
                                            <option value="false">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 市场地位指标 -->
                            <div class="indicator-category">
                                <div class="category-title">市场地位指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">行业排名</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-ranking" value="3" min="1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="30" id="unicorn-ranking-weight">
                                            <span class="weight-display" id="unicorn-ranking-weight-display">30%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">市场占有率(%)</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-market-share" value="10" min="0" step="0.1">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="20" id="unicorn-market-share-weight">
                                            <span class="weight-display" id="unicorn-market-share-weight-display">20%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">品牌影响力评分</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-brand-score" value="85" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="15" id="unicorn-brand-score-weight">
                                            <span class="weight-display" id="unicorn-brand-score-weight-display">15%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 创新引领指标 -->
                            <div class="indicator-category">
                                <div class="category-title">创新引领指标</div>
                                <div class="indicator-item">
                                    <span class="indicator-label">颠覆性技术创新</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-disruptive-tech" value="90" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="20" id="unicorn-disruptive-tech-weight">
                                            <span class="weight-display" id="unicorn-disruptive-tech-weight-display">20%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">商业模式创新</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-business-innovation" value="85" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="10" id="unicorn-business-innovation-weight">
                                            <span class="weight-display" id="unicorn-business-innovation-weight-display">10%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="indicator-item">
                                    <span class="indicator-label">生态构建能力</span>
                                    <div class="indicator-controls">
                                        <input type="number" class="indicator-input" id="unicorn-ecosystem" value="80" min="0" max="100">
                                        <div class="weight-control">
                                            <input type="range" class="weight-slider" min="0" max="100" value="5" id="unicorn-ecosystem-weight">
                                            <span class="weight-display" id="unicorn-ecosystem-weight-display">5%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; padding: 30px;">
                    <button class="btn btn-primary" onclick="saveCurrentTemplate()">保存当前配置</button>
                    <button class="btn btn-secondary" onclick="resetCurrentConfig()" style="margin-left: 15px;">重置配置</button>
                    <button class="btn btn-secondary" onclick="backToVersionManagement()" style="margin-left: 15px;">返回版本管理</button>
                </div>
            </div>

            <!-- 智能评审页面 -->
            <div id="review" class="tab-content">
                <div class="review-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                    <div>
                        <h2 style="color: #333; margin: 0;">智能评审管理</h2>
                        <p style="color: #666; margin: 5px 0 0 0;">自动化初筛 + 人工复核 + 专家评审</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="showBatchReview()">批量处理</button>
                        <button class="btn btn-secondary" onclick="exportReviewData()" style="margin-left: 10px;">导出数据</button>
                        <button class="btn btn-secondary" onclick="clearAllData()" style="margin-left: 10px; background: #dc3545; color: white;">清除数据</button>
                    </div>
                </div>

                <!-- 筛选和统计 -->
                <div class="review-filters" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                    <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                        <div>
                            <label style="font-weight: 500; margin-right: 8px;">状态筛选:</label>
                            <select id="status-filter" onchange="filterApplications()" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px;">
                                <option value="all">全部</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已拒绝</option>
                                <option value="reviewing">人工复核中</option>
                            </select>
                        </div>
                        <div>
                            <label style="font-weight: 500; margin-right: 8px;">企业类型:</label>
                            <select id="type-filter" onchange="filterApplications()" style="padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px;">
                                <option value="all">全部</option>
                                <option value="eagle">雏鹰企业</option>
                                <option value="gazelle">瞪羚企业</option>
                                <option value="unicorn">独角兽企业</option>
                            </select>
                        </div>
                        <div style="margin-left: auto;">
                            <div style="display: flex; gap: 15px; font-size: 14px;">
                                <span>待审核: <strong id="pending-count">0</strong></span>
                                <span>已审核: <strong id="reviewed-count">0</strong></span>
                                <span>通过率: <strong id="approval-rate">0%</strong></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 申报记录列表 -->
                <div class="applications-list">
                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
                        <div style="padding: 20px 25px; border-bottom: 1px solid #e9ecef; font-weight: 600; color: #333;">
                            申报记录列表
                        </div>
                        <div id="applications-table" style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: left; font-weight: 600;">企业名称</th>
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">申报类型</th>
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">机器初筛</th>
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">审核状态</th>
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">申报时间</th>
                                        <th style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-weight: 600;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="applications-tbody">
                                    <!-- 动态生成内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script>
        console.log('Script开始执行');

        // 全局变量
        let applications = JSON.parse(localStorage.getItem('applications') || '[]');
        let configTemplates = JSON.parse(localStorage.getItem('configTemplates') || '[]');
        let currentVersionInfo = JSON.parse(localStorage.getItem('currentVersionInfo') || '{"name": "默认配置", "time": "", "description": "系统默认的评价指标配置"}');
        let editingTemplate = null;
        let isNewTemplate = false;
        let currentConfig = {
            eagle: {
                // 基础条件
                age: 3,
                capital: 100,
                employeesMin: 10,
                employeesMax: 50,
                revenueMin: 500,
                revenueMax: 2000,
                // 成长性指标
                revenueGrowth: { value: 30, weight: 25 },
                profitGrowth: { value: 20, weight: 20 },
                employeeGrowth: { value: 15, weight: 15 },
                // 创新能力指标
                rdRatio: { value: 3, weight: 20 },
                patents: { value: 2, weight: 10 },
                techRatio: { value: 30, weight: 10 }
            },
            gazelle: {
                // 基础条件
                ageMin: 3,
                ageMax: 10,
                revenueMin: 2000,
                revenueMax: 100000,
                continuousGrowth: 20,
                // 成长性指标
                compoundGrowth: { value: 25, weight: 30 },
                marketGrowth: { value: 15, weight: 15 },
                expansion: { value: 70, weight: 15 },
                // 创新能力指标
                rdRatio: { value: 5, weight: 25 },
                corePatents: { value: 10, weight: 10 },
                innovationScore: { value: 80, weight: 5 }
            },
            unicorn: {
                // 基础条件
                valuation: 10,
                age: 10,
                investment: true,
                // 市场地位指标
                ranking: { value: 3, weight: 30 },
                marketShare: { value: 10, weight: 20 },
                brandScore: { value: 85, weight: 15 },
                // 创新引领指标
                disruptiveTech: { value: 90, weight: 20 },
                businessInnovation: { value: 85, weight: 10 },
                ecosystem: { value: 80, weight: 5 }
            }
        };

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');

            // 根据切换的页面执行相应操作
            if (tabName === 'version-management') {
                loadConfigTemplates();
                updateCurrentVersionInfo();
            } else if (tabName === 'review') {
                loadApplications();
                updateReviewStats();
            }
        }

        // 进入配置页面（新建模板）
        function showCreateTemplate() {
            isNewTemplate = true;
            editingTemplate = null;

            // 切换到配置页面
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById('config').classList.add('active');

            // 更新页面标题
            document.getElementById('editing-template-name').textContent = '新模板';

            // 重置配置为默认值
            resetToDefaultConfig();
            updateConfigUI();
        }

        // 编辑当前配置
        function editCurrentConfig() {
            // 找到当前启用的模板
            const activeTemplate = configTemplates.find(t => t.active);
            if (activeTemplate) {
                editTemplate(activeTemplate.id);
            } else {
                // 编辑默认配置
                isNewTemplate = false;
                editingTemplate = null;

                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById('config').classList.add('active');

                document.getElementById('editing-template-name').textContent = '默认配置';
                updateConfigUI();
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSliders();
            loadConfiguration();
            loadConfigTemplates();
            updateCurrentVersionInfo();
            loadApplications();
            updateReviewStats();

            // 生成一些示例数据
            if (applications.length === 0) {
                generateSampleData();
            }
        });



        // 测试函数
        function testFunction() {
            console.log('测试函数被调用');
            alert('测试函数工作正常');
        }



        // 编辑现有模板
        function editTemplate(templateId) {
            const template = configTemplates.find(t => t.id === templateId);
            if (!template) return;

            isNewTemplate = false;
            editingTemplate = template;

            // 切换到配置页面
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById('config').classList.add('active');

            // 更新页面标题
            document.getElementById('editing-template-name').textContent = template.name;

            // 加载模板配置
            currentConfig = JSON.parse(JSON.stringify(template.config));
            updateConfigUI();
        }

        // 编辑当前配置
        function editCurrentConfig() {
            // 找到当前启用的模板
            const activeTemplate = configTemplates.find(t => t.active);
            if (activeTemplate) {
                editTemplate(activeTemplate.id);
            } else {
                // 编辑默认配置
                isNewTemplate = false;
                editingTemplate = null;

                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById('config').classList.add('active');

                document.getElementById('editing-template-name').textContent = '默认配置';
                updateConfigUI();
            }
        }

        // 返回版本管理页面
        function backToVersionManagement() {
            // 切换回版本管理页面
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById('version-management').classList.add('active');

            // 重新加载版本管理数据
            loadConfigTemplates();
            updateCurrentVersionInfo();
        }

        // 保存当前模板
        function saveCurrentTemplate() {
            if (isNewTemplate) {
                // 新建模板，需要输入名称
                showSaveNewTemplateDialog();
            } else if (editingTemplate) {
                // 更新现有模板
                if (confirm(`确定要保存对模板"${editingTemplate.name}"的修改吗？`)) {
                    editingTemplate.config = JSON.parse(JSON.stringify(currentConfig));
                    editingTemplate.updateTime = new Date().toLocaleString('zh-CN');

                    localStorage.setItem('configTemplates', JSON.stringify(configTemplates));

                    // 如果是当前启用的模板，同时更新配置
                    if (editingTemplate.active) {
                        localStorage.setItem('enterpriseConfig', JSON.stringify(currentConfig));
                    }

                    alert('模板保存成功！');
                    backToVersionManagement();
                }
            } else {
                // 更新默认配置
                if (confirm('确定要保存对默认配置的修改吗？')) {
                    localStorage.setItem('enterpriseConfig', JSON.stringify(currentConfig));
                    alert('默认配置保存成功！');
                    backToVersionManagement();
                }
            }
        }

        // 显示保存新模板对话框
        function showSaveNewTemplateDialog() {
            const templateHtml = `
                <div style="padding: 20px 0;">
                    <div style="text-align: center; margin-bottom: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
                        <h2 style="margin: 0 0 10px 0; font-size: 1.6em;">保存新模板</h2>
                        <p style="margin: 0; opacity: 0.9;">为您的配置创建一个新的模板</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 25px; border-radius: 12px; margin-bottom: 25px;">
                        <div style="margin-bottom: 20px;">
                            <label style="font-weight: 600; margin-bottom: 10px; display: block; color: #333;">模板名称 <span style="color: red;">*</span></label>
                            <input type="text" id="new-template-name" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px;" placeholder="请输入模板名称">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="font-weight: 600; margin-bottom: 10px; display: block; color: #333;">模板描述</label>
                            <textarea id="new-template-description" rows="3" style="width: 100%; padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; resize: vertical;" placeholder="请描述此模板的用途和特点..."></textarea>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: flex; align-items: center; cursor: pointer;">
                                <input type="checkbox" id="new-template-active" checked style="margin-right: 8px; transform: scale(1.2);">
                                <span style="font-weight: 500;">保存后立即启用此模板</span>
                            </label>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="saveNewTemplate()" class="btn btn-primary" style="padding: 12px 30px; font-size: 1.1em; margin-right: 15px;">
                            保存模板
                        </button>
                        <button onclick="closeModal()" class="btn btn-secondary" style="padding: 12px 30px; font-size: 1.1em;">
                            取消
                        </button>
                    </div>
                </div>
            `;

            showModal(templateHtml, 'medium');
        }

        // 保存新模板
        function saveNewTemplate() {
            const name = document.getElementById('new-template-name').value.trim();
            const description = document.getElementById('new-template-description').value.trim();
            const active = document.getElementById('new-template-active').checked;

            if (!name) {
                alert('请输入模板名称！');
                return;
            }

            // 检查名称是否重复
            if (configTemplates.some(t => t.name === name)) {
                alert('模板名称已存在，请使用其他名称！');
                return;
            }

            const template = {
                id: Date.now().toString(),
                name: name,
                description: description || '无描述',
                config: JSON.parse(JSON.stringify(currentConfig)),
                active: active,
                createTime: new Date().toLocaleString('zh-CN'),
                updateTime: new Date().toLocaleString('zh-CN')
            };

            // 如果设置为启用，先停用其他模板
            if (active) {
                configTemplates.forEach(t => t.active = false);

                // 更新当前版本信息
                currentVersionInfo = {
                    name: name,
                    time: template.createTime,
                    description: description || '无描述'
                };
                localStorage.setItem('currentVersionInfo', JSON.stringify(currentVersionInfo));
                localStorage.setItem('enterpriseConfig', JSON.stringify(currentConfig));
            }

            configTemplates.unshift(template);
            localStorage.setItem('configTemplates', JSON.stringify(configTemplates));

            closeModal();
            alert('模板保存成功！');
            backToVersionManagement();
        }

        // 重置当前配置
        function resetCurrentConfig() {
            if (confirm('确定要重置当前配置吗？此操作将丢失未保存的修改。')) {
                if (editingTemplate) {
                    // 重新加载模板配置
                    currentConfig = JSON.parse(JSON.stringify(editingTemplate.config));
                } else {
                    // 重置为默认配置
                    resetToDefaultConfig();
                }
                updateConfigUI();
            }
        }

        // 重置为默认配置
        function resetToDefaultConfig() {
            currentConfig = {
                eagle: {
                    age: 3,
                    capital: 100,
                    employeesMin: 10,
                    employeesMax: 50,
                    revenueMin: 500,
                    revenueMax: 2000,
                    revenueGrowth: { value: 30, weight: 25 },
                    profitGrowth: { value: 20, weight: 20 },
                    employeeGrowth: { value: 15, weight: 15 },
                    rdRatio: { value: 3, weight: 20 },
                    patents: { value: 2, weight: 10 },
                    techRatio: { value: 30, weight: 10 }
                },
                gazelle: {
                    ageMin: 3,
                    ageMax: 10,
                    revenueMin: 2000,
                    revenueMax: 100000,
                    continuousGrowth: 20,
                    compoundGrowth: { value: 25, weight: 30 },
                    marketGrowth: { value: 15, weight: 15 },
                    expansion: { value: 70, weight: 15 },
                    rdRatio: { value: 5, weight: 25 },
                    corePatents: { value: 10, weight: 10 },
                    innovationScore: { value: 80, weight: 5 }
                },
                unicorn: {
                    valuation: 10,
                    age: 10,
                    investment: true,
                    ranking: { value: 3, weight: 30 },
                    marketShare: { value: 10, weight: 20 },
                    brandScore: { value: 85, weight: 15 },
                    disruptiveTech: { value: 90, weight: 20 },
                    businessInnovation: { value: 85, weight: 10 },
                    ecosystem: { value: 80, weight: 5 }
                }
            };
        }

        // 初始化滑块
        function initializeSliders() {
            const sliders = document.querySelectorAll('.weight-slider');
            sliders.forEach(slider => {
                const displayId = slider.id + '-display';
                const display = document.getElementById(displayId);

                if (display) {
                    slider.addEventListener('input', function() {
                        display.textContent = this.value + '%';
                    });
                }
            });
        }

        // 自动化初筛算法
        function automaticScreening(applicationData) {
            const type = applicationData.type;
            const config = currentConfig[type];
            let score = 0;
            let maxScore = 0;
            let details = [];

            if (type === 'eagle') {
                // 基础条件检查
                const age = calculateAge(applicationData.establishDate);
                const revenue = parseFloat(applicationData.revenue) || 0;
                const employees = parseInt(applicationData.employees) || 0;
                const capital = parseFloat(applicationData.capital) || 0;

                if (age <= config.age) details.push('✓ 成立时间符合要求');
                else details.push('✗ 成立时间超过限制');

                if (capital >= config.capital) details.push('✓ 注册资本符合要求');
                else details.push('✗ 注册资本不足');

                if (employees >= config.employeesMin && employees <= config.employeesMax) {
                    details.push('✓ 员工规模符合要求');
                } else {
                    details.push('✗ 员工规模不符合要求');
                }

                if (revenue >= config.revenueMin && revenue <= config.revenueMax) {
                    details.push('✓ 营业收入符合要求');
                } else {
                    details.push('✗ 营业收入不符合要求');
                }

                // 成长性指标评分
                const revenueGrowth = parseFloat(applicationData.revenueGrowth) || 0;
                const profitGrowth = parseFloat(applicationData.profitGrowth) || 0;

                if (revenueGrowth >= config.revenueGrowth.value) {
                    score += config.revenueGrowth.weight;
                    details.push(`✓ 营收增长率: ${revenueGrowth}%`);
                } else {
                    score += (revenueGrowth / config.revenueGrowth.value) * config.revenueGrowth.weight;
                    details.push(`△ 营收增长率: ${revenueGrowth}% (低于标准)`);
                }
                maxScore += config.revenueGrowth.weight;

                if (profitGrowth >= config.profitGrowth.value) {
                    score += config.profitGrowth.weight;
                    details.push(`✓ 利润增长率: ${profitGrowth}%`);
                } else {
                    score += (profitGrowth / config.profitGrowth.value) * config.profitGrowth.weight;
                    details.push(`△ 利润增长率: ${profitGrowth}% (低于标准)`);
                }
                maxScore += config.profitGrowth.weight;

                // 创新能力指标评分
                const rdRatio = parseFloat(applicationData.rdRatio) || 0;
                const patents = parseInt(applicationData.patents) || 0;
                const techRatio = parseFloat(applicationData.techRatio) || 0;

                if (rdRatio >= config.rdRatio.value) {
                    score += config.rdRatio.weight;
                    details.push(`✓ 研发投入占比: ${rdRatio}%`);
                } else {
                    score += (rdRatio / config.rdRatio.value) * config.rdRatio.weight;
                    details.push(`△ 研发投入占比: ${rdRatio}% (低于标准)`);
                }
                maxScore += config.rdRatio.weight;

                if (patents >= config.patents.value) {
                    score += config.patents.weight;
                    details.push(`✓ 专利申请数量: ${patents}件`);
                } else {
                    score += (patents / config.patents.value) * config.patents.weight;
                    details.push(`△ 专利申请数量: ${patents}件 (低于标准)`);
                }
                maxScore += config.patents.weight;

                if (techRatio >= config.techRatio.value) {
                    score += config.techRatio.weight;
                    details.push(`✓ 技术人员占比: ${techRatio}%`);
                } else {
                    score += (techRatio / config.techRatio.value) * config.techRatio.weight;
                    details.push(`△ 技术人员占比: ${techRatio}% (低于标准)`);
                }
                maxScore += config.techRatio.weight;
            }

            } else if (type === 'gazelle') {
                // 瞪羚企业评分逻辑
                const age = calculateAge(applicationData.establishDate);
                const revenue = parseFloat(applicationData.revenue) || 0;
                const revenueGrowth = parseFloat(applicationData.revenueGrowth) || 0;
                const profitGrowth = parseFloat(applicationData.profitGrowth) || 0;
                const rdRatio = parseFloat(applicationData.rdRatio) || 0;
                const patents = parseInt(applicationData.patents) || 0;
                const marketShare = parseFloat(applicationData.marketShare) || 0;

                // 基础条件检查
                if (age >= config.ageMin && age <= config.ageMax) details.push('✓ 成立时间符合要求');
                else details.push('✗ 成立时间不符合要求');

                if (revenue >= config.revenueMin && revenue <= config.revenueMax) {
                    details.push('✓ 营业收入符合要求');
                } else {
                    details.push('✗ 营业收入不符合要求');
                }

                // 成长性指标评分
                if (revenueGrowth >= config.compoundGrowth.value) {
                    score += config.compoundGrowth.weight;
                    details.push(`✓ 营收复合增长率: ${revenueGrowth}%`);
                } else {
                    score += (revenueGrowth / config.compoundGrowth.value) * config.compoundGrowth.weight;
                    details.push(`△ 营收复合增长率: ${revenueGrowth}% (低于标准)`);
                }
                maxScore += config.compoundGrowth.weight;

                if (marketShare >= config.marketGrowth.value) {
                    score += config.marketGrowth.weight;
                    details.push(`✓ 市场份额: ${marketShare}%`);
                } else {
                    score += (marketShare / config.marketGrowth.value) * config.marketGrowth.weight;
                    details.push(`△ 市场份额: ${marketShare}% (低于标准)`);
                }
                maxScore += config.marketGrowth.weight;

                // 创新能力指标评分
                if (rdRatio >= config.rdRatio.value) {
                    score += config.rdRatio.weight;
                    details.push(`✓ 研发投入占比: ${rdRatio}%`);
                } else {
                    score += (rdRatio / config.rdRatio.value) * config.rdRatio.weight;
                    details.push(`△ 研发投入占比: ${rdRatio}% (低于标准)`);
                }
                maxScore += config.rdRatio.weight;

                if (patents >= config.corePatents.value) {
                    score += config.corePatents.weight;
                    details.push(`✓ 核心技术专利: ${patents}件`);
                } else {
                    score += (patents / config.corePatents.value) * config.corePatents.weight;
                    details.push(`△ 核心技术专利: ${patents}件 (低于标准)`);
                }
                maxScore += config.corePatents.weight;

            } else if (type === 'unicorn') {
                // 独角兽企业评分逻辑
                const age = calculateAge(applicationData.establishDate);
                const valuation = parseFloat(applicationData.valuation) || 0;
                const revenue = parseFloat(applicationData.revenue) || 0;
                const marketShare = parseFloat(applicationData.marketShare) || 0;
                const rdRatio = parseFloat(applicationData.rdRatio) || 0;
                const patents = parseInt(applicationData.patents) || 0;

                // 基础条件检查
                if (valuation >= config.valuation) details.push('✓ 企业估值符合要求');
                else details.push('✗ 企业估值不符合要求');

                if (age <= config.age) details.push('✓ 成立时间符合要求');
                else details.push('✗ 成立时间超过限制');

                // 市场地位指标评分
                if (marketShare >= config.marketShare.value) {
                    score += config.marketShare.weight;
                    details.push(`✓ 市场占有率: ${marketShare}%`);
                } else {
                    score += (marketShare / config.marketShare.value) * config.marketShare.weight;
                    details.push(`△ 市场占有率: ${marketShare}% (低于标准)`);
                }
                maxScore += config.marketShare.weight;

                // 创新引领指标评分
                if (rdRatio >= 10) { // 独角兽企业研发投入要求更高
                    score += config.disruptiveTech.weight;
                    details.push(`✓ 颠覆性技术创新: 研发投入${rdRatio}%`);
                } else {
                    score += (rdRatio / 10) * config.disruptiveTech.weight;
                    details.push(`△ 颠覆性技术创新: 研发投入${rdRatio}% (需要更高投入)`);
                }
                maxScore += config.disruptiveTech.weight;

                if (patents >= 20) { // 独角兽企业专利要求更高
                    score += config.businessInnovation.weight;
                    details.push(`✓ 商业模式创新: ${patents}件专利`);
                } else {
                    score += (patents / 20) * config.businessInnovation.weight;
                    details.push(`△ 商业模式创新: ${patents}件专利 (需要更多专利)`);
                }
                maxScore += config.businessInnovation.weight;
            }

            const finalScore = maxScore > 0 ? (score / maxScore) * 100 : 0;
            const passed = finalScore >= 60; // 60分及格

            return {
                score: finalScore,
                passed: passed,
                details: details,
                recommendation: passed ? '建议通过初筛' : '建议人工复核'
            };
        }

        // 计算企业成立年限
        function calculateAge(establishDate) {
            if (!establishDate) return 0;
            const establish = new Date(establishDate);
            const now = new Date();
            return Math.floor((now - establish) / (365.25 * 24 * 60 * 60 * 1000));
        }



        // 保存配置
        function saveConfiguration() {
            // 收集所有配置数据
            const newConfig = {
                eagle: {
                    age: parseInt(document.getElementById('eagle-age').value) || 3,
                    capital: parseInt(document.getElementById('eagle-capital').value) || 100,
                    employeesMin: parseInt(document.getElementById('eagle-employees-min').value) || 10,
                    employeesMax: parseInt(document.getElementById('eagle-employees-max').value) || 50,
                    revenueMin: parseInt(document.getElementById('eagle-revenue-min').value) || 500,
                    revenueMax: parseInt(document.getElementById('eagle-revenue-max').value) || 2000,
                    revenueGrowth: {
                        value: parseInt(document.getElementById('eagle-revenue-growth').value) || 30,
                        weight: parseInt(document.getElementById('eagle-revenue-growth-weight').value) || 25
                    },
                    profitGrowth: {
                        value: parseInt(document.getElementById('eagle-profit-growth').value) || 20,
                        weight: parseInt(document.getElementById('eagle-profit-growth-weight').value) || 20
                    },
                    employeeGrowth: {
                        value: parseInt(document.getElementById('eagle-employee-growth').value) || 15,
                        weight: parseInt(document.getElementById('eagle-employee-growth-weight').value) || 15
                    },
                    rdRatio: {
                        value: parseInt(document.getElementById('eagle-rd-ratio').value) || 3,
                        weight: parseInt(document.getElementById('eagle-rd-ratio-weight').value) || 20
                    },
                    patents: {
                        value: parseInt(document.getElementById('eagle-patents').value) || 2,
                        weight: parseInt(document.getElementById('eagle-patents-weight').value) || 10
                    },
                    techRatio: {
                        value: parseInt(document.getElementById('eagle-tech-ratio').value) || 30,
                        weight: parseInt(document.getElementById('eagle-tech-ratio-weight').value) || 10
                    }
                }
                // 类似地处理gazelle和unicorn配置...
            };

            currentConfig = newConfig;
            localStorage.setItem('enterpriseConfig', JSON.stringify(currentConfig));
            alert('配置已保存！');
        }

        // 加载配置
        function loadConfiguration() {
            const savedConfig = localStorage.getItem('enterpriseConfig');
            if (savedConfig) {
                currentConfig = JSON.parse(savedConfig);
                updateConfigUI();
            }
        }

        // 更新配置界面
        function updateConfigUI() {
            // 更新雏鹰企业配置界面
            if (document.getElementById('eagle-age')) {
                document.getElementById('eagle-age').value = currentConfig.eagle.age;
                document.getElementById('eagle-capital').value = currentConfig.eagle.capital;
                document.getElementById('eagle-employees-min').value = currentConfig.eagle.employeesMin;
                document.getElementById('eagle-employees-max').value = currentConfig.eagle.employeesMax;
                document.getElementById('eagle-revenue-min').value = currentConfig.eagle.revenueMin;
                document.getElementById('eagle-revenue-max').value = currentConfig.eagle.revenueMax;

                // 更新权重滑块和显示
                document.getElementById('eagle-revenue-growth').value = currentConfig.eagle.revenueGrowth.value;
                document.getElementById('eagle-revenue-growth-weight').value = currentConfig.eagle.revenueGrowth.weight;
                document.getElementById('eagle-revenue-growth-weight-display').textContent = currentConfig.eagle.revenueGrowth.weight + '%';

                // 其他字段类似更新...
            }
        }

        // 加载申报记录
        function loadApplications() {
            const tbody = document.getElementById('applications-tbody');
            if (!tbody) return;

            if (applications.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="padding: 30px; text-align: center; color: #666;">暂无申报记录</td></tr>';
                return;
            }

            tbody.innerHTML = '';
            applications.forEach(app => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6;">
                        <div style="font-weight: 500;">${app.companyName}</div>
                        <div style="font-size: 12px; color: #666;">${app.contact || '未填写'}</div>
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <span class="status-badge ${app.type === 'eagle' ? 'status-pending' : app.type === 'gazelle' ? 'status-reviewing' : 'status-approved'}">
                            ${app.type === 'eagle' ? '🦅 雏鹰' : app.type === 'gazelle' ? '🦌 瞪羚' : '🦄 独角兽'}
                        </span>
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <div class="score-badge ${app.screening.score >= 80 ? 'score-high' : app.screening.score >= 60 ? 'score-medium' : 'score-low'}">
                            ${app.screening.score.toFixed(1)}分
                        </div>
                        <div style="font-size: 11px; color: #666; margin-top: 2px;">${app.screening.recommendation}</div>
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <span class="status-badge status-${app.status}">
                            ${getStatusText(app.status)}
                        </span>
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center; font-size: 13px; color: #666;">
                        ${app.submitTime}
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <button onclick="viewApplicationDetail(${app.id})" style="padding: 4px 8px; margin-right: 5px; border: 1px solid #667eea; background: white; color: #667eea; border-radius: 4px; cursor: pointer; font-size: 12px;">详情</button>
                        ${app.status === 'pending' ? `<button onclick="reviewApplication(${app.id})" style="padding: 4px 8px; border: 1px solid #28a745; background: white; color: #28a745; border-radius: 4px; cursor: pointer; font-size: 12px;">审核</button>` : ''}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝',
                'reviewing': '复核中'
            };
            return statusMap[status] || status;
        }

        // 更新评审统计
        function updateReviewStats() {
            const pendingCount = applications.filter(app => app.status === 'pending').length;
            const reviewedCount = applications.filter(app => app.status !== 'pending').length;
            const approvedCount = applications.filter(app => app.status === 'approved').length;
            const approvalRate = reviewedCount > 0 ? ((approvedCount / reviewedCount) * 100).toFixed(1) : 0;

            if (document.getElementById('pending-count')) {
                document.getElementById('pending-count').textContent = pendingCount;
                document.getElementById('reviewed-count').textContent = reviewedCount;
                document.getElementById('approval-rate').textContent = approvalRate + '%';
            }
        }

        // 查看申报详情
        function viewApplicationDetail(appId) {
            const app = applications.find(a => a.id === appId);
            if (!app) return;

            const age = calculateAge(app.establishDate);
            const typeInfo = {
                eagle: { name: '🦅 雏鹰企业', color: '#ff6b6b', bg: '#fff5f5' },
                gazelle: { name: '🦌 瞪羚企业', color: '#4ecdc4', bg: '#f0fffe' },
                unicorn: { name: '🦄 独角兽企业', color: '#ffe66d', bg: '#fffef0' }
            };

            let detailHtml = `
                <div style="padding: 20px 0;">
                    <!-- 头部信息 -->
                    <div style="text-align: center; margin-bottom: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
                        <h2 style="margin: 0 0 10px 0; font-size: 1.8em;">${app.companyName}</h2>
                        <div style="display: inline-block; background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 1.1em;">
                            ${typeInfo[app.type].name}
                        </div>
                        <div style="margin-top: 15px; font-size: 0.9em; opacity: 0.9;">
                            申报时间: ${app.submitTime} | 联系人: ${app.contact}
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                        <!-- 左侧信息 -->
                        <div>
                            <!-- 基本信息 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #667eea;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">📋 基本信息</h4>
                                <div style="display: grid; gap: 10px;">
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">成立时间</span>
                                        <span style="font-weight: 500;">${app.establishDate} (${age}年)</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">注册资本</span>
                                        <span style="font-weight: 500;">${app.capital || '未填写'}万元</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                        <span style="color: #666;">员工人数</span>
                                        <span style="font-weight: 500;">${app.employees}人</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 财务指标 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">💰 财务指标</h4>
                                <div style="display: grid; gap: 10px;">
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">年营业收入</span>
                                        <span style="font-weight: 500; color: #28a745;">${app.revenue}万元</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">营收增长率</span>
                                        <span style="font-weight: 500; color: ${(app.revenueGrowth || 0) >= 30 ? '#28a745' : '#ffc107'};">${app.revenueGrowth || '未填写'}%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; ${app.valuation ? 'border-bottom: 1px solid #e9ecef;' : ''}">
                                        <span style="color: #666;">利润增长率</span>
                                        <span style="font-weight: 500; color: ${(app.profitGrowth || 0) >= 20 ? '#28a745' : '#ffc107'};">${app.profitGrowth || '未填写'}%</span>
                                    </div>
                                    ${app.valuation ? `
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                        <span style="color: #666;">企业估值</span>
                                        <span style="font-weight: 500; color: #dc3545;">${app.valuation}亿美元</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- 创新指标 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; border-left: 4px solid #17a2b8;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">🔬 创新指标</h4>
                                <div style="display: grid; gap: 10px;">
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">研发投入占比</span>
                                        <span style="font-weight: 500; color: ${(app.rdRatio || 0) >= 5 ? '#28a745' : '#ffc107'};">${app.rdRatio}%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                        <span style="color: #666;">专利申请数量</span>
                                        <span style="font-weight: 500;">${app.patents || '未填写'}件</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; ${app.marketShare ? 'border-bottom: 1px solid #e9ecef;' : ''}">
                                        <span style="color: #666;">技术人员占比</span>
                                        <span style="font-weight: 500;">${app.techRatio || '未填写'}%</span>
                                    </div>
                                    ${app.marketShare ? `
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                        <span style="color: #666;">市场占有率</span>
                                        <span style="font-weight: 500; color: #17a2b8;">${app.marketShare}%</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>

                        <!-- 右侧信息 -->
                        <div>
                            <!-- 机器初筛结果 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #6f42c1;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">🤖 机器初筛结果</h4>
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="display: inline-block; padding: 15px 25px; border-radius: 50px; background: ${app.screening.score >= 80 ? '#28a745' : app.screening.score >= 60 ? '#ffc107' : '#dc3545'}; color: white; font-size: 1.5em; font-weight: 600; margin-bottom: 10px;">
                                        ${app.screening.score.toFixed(1)}分
                                    </div>
                                    <div style="color: #666; font-size: 0.9em;">${app.screening.recommendation}</div>
                                </div>
                                <div style="background: white; padding: 15px; border-radius: 8px;">
                                    <strong style="color: #333; margin-bottom: 10px; display: block;">详细评估:</strong>
                                    <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                                        ${app.screening.details.map(detail => `
                                            <li style="margin: 8px 0; color: ${detail.startsWith('✓') ? '#28a745' : detail.startsWith('✗') ? '#dc3545' : '#ffc107'};">
                                                ${detail}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            </div>

                            <!-- 审核状态 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #fd7e14;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">📝 审核状态</h4>
                                <div style="text-align: center; margin-bottom: 15px;">
                                    <span class="status-badge status-${app.status}" style="font-size: 1.1em; padding: 8px 16px;">
                                        ${getStatusText(app.status)}
                                    </span>
                                </div>
                                ${app.reviewTime ? `
                                    <div style="background: white; padding: 15px; border-radius: 8px;">
                                        <div style="margin-bottom: 10px;"><strong>审核时间:</strong> ${app.reviewTime}</div>
                                        ${app.reviewComment ? `<div><strong>审核意见:</strong> ${app.reviewComment}</div>` : ''}
                                    </div>
                                ` : '<div style="text-align: center; color: #666; font-style: italic;">等待审核中...</div>'}
                            </div>

                            ${app.description ? `
                            <!-- 企业简介 -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; border-left: 4px solid #e83e8c;">
                                <h4 style="color: #333; margin: 0 0 15px 0; font-size: 1.2em;">📄 企业简介</h4>
                                <div style="background: white; padding: 15px; border-radius: 8px; line-height: 1.6; color: #555;">
                                    ${app.description}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        ${app.status === 'pending' ? `
                            <button onclick="closeModal(); setTimeout(() => reviewApplication(${app.id}), 100);" class="btn btn-primary" style="margin-right: 15px;">
                                立即审核
                            </button>
                        ` : ''}
                        <button onclick="closeModal()" class="btn btn-secondary">关闭详情</button>
                    </div>
                </div>
            `;

            showModal(detailHtml, 'large');
        }

        // 审核申报
        function reviewApplication(appId) {
            const app = applications.find(a => a.id === appId);
            if (!app) return;

            const typeInfo = {
                eagle: { name: '🦅 雏鹰企业', color: '#ff6b6b' },
                gazelle: { name: '🦌 瞪羚企业', color: '#4ecdc4' },
                unicorn: { name: '🦄 独角兽企业', color: '#ffe66d' }
            };

            const reviewHtml = `
                <div style="padding: 20px 0;">
                    <!-- 头部信息 -->
                    <div style="text-align: center; margin-bottom: 30px; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
                        <h2 style="margin: 0 0 10px 0; font-size: 1.6em;">审核申报</h2>
                        <div style="font-size: 1.3em; margin-bottom: 5px;">${app.companyName}</div>
                        <div style="display: inline-block; background: rgba(255,255,255,0.2); padding: 6px 12px; border-radius: 15px; font-size: 0.9em;">
                            ${typeInfo[app.type].name}
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                        <!-- 左侧：机器初筛结果 -->
                        <div>
                            <div style="background: #f8f9fa; padding: 25px; border-radius: 12px; border-left: 4px solid #6f42c1;">
                                <h4 style="color: #333; margin: 0 0 20px 0; font-size: 1.2em;">🤖 机器初筛结果</h4>

                                <div style="text-align: center; margin-bottom: 25px;">
                                    <div style="display: inline-block; padding: 20px 30px; border-radius: 50px; background: ${app.screening.score >= 80 ? '#28a745' : app.screening.score >= 60 ? '#ffc107' : '#dc3545'}; color: white; font-size: 2em; font-weight: 600; margin-bottom: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                                        ${app.screening.score.toFixed(1)}分
                                    </div>
                                    <div style="color: #666; font-size: 1.1em; font-weight: 500;">${app.screening.recommendation}</div>
                                </div>

                                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <strong style="color: #333; margin-bottom: 15px; display: block; font-size: 1.1em;">详细评估结果:</strong>
                                    <div style="max-height: 200px; overflow-y: auto;">
                                        <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                                            ${app.screening.details.map(detail => `
                                                <li style="margin: 10px 0; color: ${detail.startsWith('✓') ? '#28a745' : detail.startsWith('✗') ? '#dc3545' : '#ffc107'}; font-size: 0.95em;">
                                                    ${detail}
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：审核决定 -->
                        <div>
                            <div style="background: #f8f9fa; padding: 25px; border-radius: 12px; border-left: 4px solid #28a745;">
                                <h4 style="color: #333; margin: 0 0 20px 0; font-size: 1.2em;">✅ 人工审核</h4>

                                <div style="margin-bottom: 25px;">
                                    <label style="font-weight: 600; margin-bottom: 15px; display: block; color: #333; font-size: 1.1em;">审核决定:</label>
                                    <div style="display: grid; gap: 15px;">
                                        <label style="display: flex; align-items: center; padding: 15px; background: white; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;" onmouseover="this.style.borderColor='#28a745'; this.style.boxShadow='0 2px 8px rgba(40,167,69,0.2)'" onmouseout="this.style.borderColor='transparent'; this.style.boxShadow='none'">
                                            <input type="radio" id="approve" name="decision" value="approved" style="margin-right: 12px; transform: scale(1.2);">
                                            <div>
                                                <div style="font-weight: 500; color: #28a745; font-size: 1.1em;">✅ 通过</div>
                                                <div style="font-size: 0.85em; color: #666; margin-top: 2px;">符合申报标准，建议通过</div>
                                            </div>
                                        </label>

                                        <label style="display: flex; align-items: center; padding: 15px; background: white; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;" onmouseover="this.style.borderColor='#dc3545'; this.style.boxShadow='0 2px 8px rgba(220,53,69,0.2)'" onmouseout="this.style.borderColor='transparent'; this.style.boxShadow='none'">
                                            <input type="radio" id="reject" name="decision" value="rejected" style="margin-right: 12px; transform: scale(1.2);">
                                            <div>
                                                <div style="font-weight: 500; color: #dc3545; font-size: 1.1em;">❌ 拒绝</div>
                                                <div style="font-size: 0.85em; color: #666; margin-top: 2px;">不符合申报标准，拒绝申请</div>
                                            </div>
                                        </label>

                                        <label style="display: flex; align-items: center; padding: 15px; background: white; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;" onmouseover="this.style.borderColor='#ffc107'; this.style.boxShadow='0 2px 8px rgba(255,193,7,0.2)'" onmouseout="this.style.borderColor='transparent'; this.style.boxShadow='none'">
                                            <input type="radio" id="review-more" name="decision" value="reviewing" style="margin-right: 12px; transform: scale(1.2);">
                                            <div>
                                                <div style="font-weight: 500; color: #ffc107; font-size: 1.1em;">🔍 进一步复核</div>
                                                <div style="font-size: 0.85em; color: #666; margin-top: 2px;">需要补充材料或专家评审</div>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <div style="margin-bottom: 25px;">
                                    <label style="font-weight: 600; margin-bottom: 10px; display: block; color: #333; font-size: 1.1em;">审核意见:</label>
                                    <textarea id="review-comment" rows="5" style="width: 100%; padding: 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; line-height: 1.5; resize: vertical; transition: border-color 0.3s ease;" placeholder="请详细说明审核理由和建议..." onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e9ecef'"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="text-align: center; padding-top: 20px; border-top: 1px solid #e9ecef;">
                        <button onclick="submitReview(${appId})" class="btn btn-primary" style="padding: 12px 30px; font-size: 1.1em; margin-right: 15px;">
                            提交审核结果
                        </button>
                        <button onclick="closeModal()" class="btn btn-secondary" style="padding: 12px 30px; font-size: 1.1em;">
                            取消审核
                        </button>
                    </div>
                </div>
            `;

            showModal(reviewHtml, 'large');
        }

        // 提交审核结果
        function submitReview(appId) {
            const decision = document.querySelector('input[name="decision"]:checked');
            const comment = document.getElementById('review-comment').value;

            if (!decision) {
                alert('请选择审核决定！');
                return;
            }

            const app = applications.find(a => a.id === appId);
            if (app) {
                app.status = decision.value;
                app.reviewComment = comment;
                app.reviewTime = new Date().toLocaleString('zh-CN');

                localStorage.setItem('applications', JSON.stringify(applications));

                closeModal();
                loadApplications();
                updateReviewStats();

                alert('审核结果已提交！');
            }
        }

        // 显示模态框
        function showModal(content, size = 'large') {
            const modal = document.createElement('div');
            modal.id = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.6);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                backdrop-filter: blur(3px);
                animation: fadeIn 0.3s ease-out;
            `;

            const modalContent = document.createElement('div');
            let maxWidth = '90vw';
            let maxHeight = '90vh';

            if (size === 'large') {
                maxWidth = '1000px';
                maxHeight = '80vh';
            } else if (size === 'medium') {
                maxWidth = '700px';
                maxHeight = '70vh';
            }

            modalContent.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 16px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                max-width: ${maxWidth};
                max-height: ${maxHeight};
                width: 95vw;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease-out;
            `;
            modalContent.innerHTML = content;

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                font-size: 28px;
                color: #999;
                cursor: pointer;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
            `;
            closeBtn.onmouseover = () => {
                closeBtn.style.background = '#f5f5f5';
                closeBtn.style.color = '#333';
            };
            closeBtn.onmouseout = () => {
                closeBtn.style.background = 'none';
                closeBtn.style.color = '#999';
            };
            closeBtn.onclick = closeModal;

            modalContent.appendChild(closeBtn);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px) scale(0.9);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }
            `;
            document.head.appendChild(style);

            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.remove();
            }
        }

        // 筛选申报记录
        function filterApplications() {
            const statusFilter = document.getElementById('status-filter').value;
            const typeFilter = document.getElementById('type-filter').value;

            let filteredApps = applications;

            if (statusFilter !== 'all') {
                filteredApps = filteredApps.filter(app => app.status === statusFilter);
            }

            if (typeFilter !== 'all') {
                filteredApps = filteredApps.filter(app => app.type === typeFilter);
            }

            // 临时替换applications数组来显示筛选结果
            const originalApps = applications;
            applications = filteredApps;
            loadApplications();
            applications = originalApps;
        }

        // 批量处理
        function showBatchReview() {
            const pendingApps = applications.filter(app => app.status === 'pending');
            if (pendingApps.length === 0) {
                alert('暂无待审核的申报记录！');
                return;
            }

            let batchHtml = `
                <div style="max-width: 700px; max-height: 70vh; overflow-y: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">批量审核 (${pendingApps.length}条记录)</h3>

                    <div style="margin-bottom: 20px;">
                        <button onclick="batchApprove()" class="btn btn-primary" style="margin-right: 10px;">批量通过高分申报</button>
                        <button onclick="batchReject()" class="btn btn-secondary">批量拒绝低分申报</button>
                    </div>

                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">企业名称</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">类型</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">得分</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">建议</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${pendingApps.map(app => `
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #dee2e6;">${app.companyName}</td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                                        ${app.type === 'eagle' ? '🦅 雏鹰' : app.type === 'gazelle' ? '🦌 瞪羚' : '🦄 独角兽'}
                                    </td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                                        <span class="score-badge ${app.screening.score >= 80 ? 'score-high' : app.screening.score >= 60 ? 'score-medium' : 'score-low'}">
                                            ${app.screening.score.toFixed(1)}
                                        </span>
                                    </td>
                                    <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; font-size: 12px;">
                                        ${app.screening.recommendation}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn btn-secondary">关闭</button>
                    </div>
                </div>
            `;

            showModal(batchHtml);
        }

        // 批量通过高分申报
        function batchApprove() {
            const highScoreApps = applications.filter(app =>
                app.status === 'pending' && app.screening.score >= 80
            );

            if (highScoreApps.length === 0) {
                alert('没有符合条件的高分申报记录！');
                return;
            }

            if (confirm(`确定要批量通过 ${highScoreApps.length} 条高分申报记录吗？`)) {
                highScoreApps.forEach(app => {
                    app.status = 'approved';
                    app.reviewComment = '批量审核：高分自动通过';
                    app.reviewTime = new Date().toLocaleString('zh-CN');
                });

                localStorage.setItem('applications', JSON.stringify(applications));
                closeModal();
                loadApplications();
                updateReviewStats();
                alert(`已批量通过 ${highScoreApps.length} 条申报记录！`);
            }
        }

        // 批量拒绝低分申报
        function batchReject() {
            const lowScoreApps = applications.filter(app =>
                app.status === 'pending' && app.screening.score < 40
            );

            if (lowScoreApps.length === 0) {
                alert('没有符合条件的低分申报记录！');
                return;
            }

            if (confirm(`确定要批量拒绝 ${lowScoreApps.length} 条低分申报记录吗？`)) {
                lowScoreApps.forEach(app => {
                    app.status = 'rejected';
                    app.reviewComment = '批量审核：低分自动拒绝';
                    app.reviewTime = new Date().toLocaleString('zh-CN');
                });

                localStorage.setItem('applications', JSON.stringify(applications));
                closeModal();
                loadApplications();
                updateReviewStats();
                alert(`已批量拒绝 ${lowScoreApps.length} 条申报记录！`);
            }
        }

        // 计算评分
        function calculateScores(companyData) {
            const scores = { eagle: 0, gazelle: 0, unicorn: 0 };

            // 雏鹰企业评分
            if (companyData.revenue >= currentConfig.eagle.revenue.value) {
                scores.eagle += currentConfig.eagle.revenue.weight;
            } else {
                scores.eagle += (companyData.revenue / currentConfig.eagle.revenue.value) * currentConfig.eagle.revenue.weight;
            }

            if (companyData.rd >= currentConfig.eagle.rd.value) {
                scores.eagle += currentConfig.eagle.rd.weight;
            } else {
                scores.eagle += (companyData.rd / currentConfig.eagle.rd.value) * currentConfig.eagle.rd.weight;
            }

            if (companyData.age >= currentConfig.eagle.age.value) {
                scores.eagle += currentConfig.eagle.age.weight;
            } else {
                scores.eagle += (companyData.age / currentConfig.eagle.age.value) * currentConfig.eagle.age.weight;
            }

            if (companyData.employees >= currentConfig.eagle.employees.value) {
                scores.eagle += currentConfig.eagle.employees.weight;
            } else {
                scores.eagle += (companyData.employees / currentConfig.eagle.employees.value) * currentConfig.eagle.employees.weight;
            }

            // 瞪羚企业评分
            if (companyData.revenue >= currentConfig.gazelle.revenue.value) {
                scores.gazelle += currentConfig.gazelle.revenue.weight;
            } else {
                scores.gazelle += (companyData.revenue / currentConfig.gazelle.revenue.value) * currentConfig.gazelle.revenue.weight;
            }

            if (companyData.growth >= currentConfig.gazelle.growth.value) {
                scores.gazelle += currentConfig.gazelle.growth.weight;
            } else {
                scores.gazelle += (companyData.growth / currentConfig.gazelle.growth.value) * currentConfig.gazelle.growth.weight;
            }

            if (companyData.rd >= currentConfig.gazelle.rd.value) {
                scores.gazelle += currentConfig.gazelle.rd.weight;
            } else {
                scores.gazelle += (companyData.rd / currentConfig.gazelle.rd.value) * currentConfig.gazelle.rd.weight;
            }

            if (companyData.employees >= currentConfig.gazelle.employees.value) {
                scores.gazelle += currentConfig.gazelle.employees.weight;
            } else {
                scores.gazelle += (companyData.employees / currentConfig.gazelle.employees.value) * currentConfig.gazelle.employees.weight;
            }

            // 独角兽企业评分
            if (companyData.valuation >= currentConfig.unicorn.valuation.value) {
                scores.unicorn += currentConfig.unicorn.valuation.weight;
            } else {
                scores.unicorn += (companyData.valuation / currentConfig.unicorn.valuation.value) * currentConfig.unicorn.valuation.weight;
            }

            const revenueInBillion = companyData.revenue / 10000; // 转换为亿元
            if (revenueInBillion >= currentConfig.unicorn.revenue.value) {
                scores.unicorn += currentConfig.unicorn.revenue.weight;
            } else {
                scores.unicorn += (revenueInBillion / currentConfig.unicorn.revenue.value) * currentConfig.unicorn.revenue.weight;
            }

            if (companyData.innovation >= currentConfig.unicorn.innovation.value) {
                scores.unicorn += currentConfig.unicorn.innovation.weight;
            } else {
                scores.unicorn += (companyData.innovation / currentConfig.unicorn.innovation.value) * currentConfig.unicorn.innovation.weight;
            }

            if (companyData.market >= currentConfig.unicorn.market.value) {
                scores.unicorn += currentConfig.unicorn.market.weight;
            } else {
                scores.unicorn += (companyData.market / currentConfig.unicorn.market.value) * currentConfig.unicorn.market.weight;
            }

            // 确保分数在0-100之间
            scores.eagle = Math.min(100, Math.max(0, scores.eagle));
            scores.gazelle = Math.min(100, Math.max(0, scores.gazelle));
            scores.unicorn = Math.min(100, Math.max(0, scores.unicorn));

            return scores;
        }

        // 显示评估结果
        function displayResults(companyData, scores, category, categoryIcon, maxScore) {
            document.getElementById('final-category').textContent = categoryIcon + ' ' + category;
            document.getElementById('final-score').textContent = maxScore.toFixed(1);
            document.getElementById('confidence-level').textContent = getConfidenceLevel(maxScore) + '%';

            // 更新匹配度进度条
            document.getElementById('eagle-match-score').textContent = scores.eagle.toFixed(1) + '%';
            document.getElementById('eagle-progress').style.width = scores.eagle + '%';

            document.getElementById('gazelle-match-score').textContent = scores.gazelle.toFixed(1) + '%';
            document.getElementById('gazelle-progress').style.width = scores.gazelle + '%';

            document.getElementById('unicorn-match-score').textContent = scores.unicorn.toFixed(1) + '%';
            document.getElementById('unicorn-progress').style.width = scores.unicorn + '%';

            // 生成改进建议
            generateRecommendations(companyData, scores, category);

            // 显示结果区域
            document.getElementById('evaluation-result').style.display = 'block';
            document.getElementById('evaluation-result').scrollIntoView({ behavior: 'smooth' });
        }

        // 获取置信度
        function getConfidenceLevel(score) {
            if (score >= 80) return 95;
            if (score >= 60) return 85;
            if (score >= 40) return 70;
            return 50;
        }

        // 生成改进建议
        function generateRecommendations(companyData, scores, category) {
            const recommendations = [];

            if (category === '雏鹰企业' || category === '未分类') {
                if (companyData.revenue < currentConfig.eagle.revenue.value) {
                    recommendations.push('建议提升营业收入规模，目标达到' + currentConfig.eagle.revenue.value + '万元以上');
                }
                if (companyData.rd < currentConfig.eagle.rd.value) {
                    recommendations.push('建议加大研发投入，研发占比提升至' + currentConfig.eagle.rd.value + '%以上');
                }
                if (companyData.employees < currentConfig.eagle.employees.value) {
                    recommendations.push('建议扩大团队规模，员工数量达到' + currentConfig.eagle.employees.value + '人以上');
                }
            }

            if (category === '瞪羚企业' || (category === '雏鹰企业' && scores.gazelle > 30)) {
                if (companyData.growth < currentConfig.gazelle.growth.value) {
                    recommendations.push('建议保持高速增长，年增长率达到' + currentConfig.gazelle.growth.value + '%以上');
                }
                if (companyData.revenue < currentConfig.gazelle.revenue.value) {
                    recommendations.push('建议继续扩大营收规模，目标' + currentConfig.gazelle.revenue.value + '万元以上');
                }
            }

            if (scores.unicorn > 20) {
                if (companyData.valuation < currentConfig.unicorn.valuation.value) {
                    recommendations.push('建议提升企业估值，目标达到' + currentConfig.unicorn.valuation.value + '亿元以上');
                }
                if (companyData.innovation < currentConfig.unicorn.innovation.value) {
                    recommendations.push('建议加强技术创新能力，创新指数提升至' + currentConfig.unicorn.innovation.value + '以上');
                }
                if (companyData.market < currentConfig.unicorn.market.value) {
                    recommendations.push('建议扩大市场占有率，目标达到' + currentConfig.unicorn.market.value + '%以上');
                }
            }

            if (recommendations.length === 0) {
                recommendations.push('企业发展良好，建议继续保持当前发展态势');
            }

            const recommendationList = document.getElementById('recommendation-list');
            recommendationList.innerHTML = '';
            recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.textContent = rec;
                li.style.marginBottom = '8px';
                recommendationList.appendChild(li);
            });
        }

        // 保存到历史记录
        function saveToHistory(companyData, category, score) {
            const record = {
                id: Date.now(),
                name: companyData.name,
                category: category,
                score: score.toFixed(1),
                timestamp: new Date().toLocaleString('zh-CN'),
                data: companyData
            };

            evaluationHistory.unshift(record);

            // 限制历史记录数量
            if (evaluationHistory.length > 50) {
                evaluationHistory = evaluationHistory.slice(0, 50);
            }

            localStorage.setItem('evaluationHistory', JSON.stringify(evaluationHistory));
            updateAnalysisData();
        }

        // 清空表单
        function clearForm() {
            document.getElementById('company-name').value = '';
            document.getElementById('company-revenue').value = '';
            document.getElementById('company-growth').value = '';
            document.getElementById('company-rd').value = '';
            document.getElementById('company-age').value = '';
            document.getElementById('company-employees').value = '';
            document.getElementById('company-valuation').value = '';
            document.getElementById('company-innovation').value = '';
            document.getElementById('company-market').value = '';

            document.getElementById('evaluation-result').style.display = 'none';
        }

        // 更新分析数据
        function updateAnalysisData() {
            const totalEvaluations = evaluationHistory.length;
            const eagleCount = evaluationHistory.filter(record => record.category.includes('雏鹰')).length;
            const gazelleCount = evaluationHistory.filter(record => record.category.includes('瞪羚')).length;
            const unicornCount = evaluationHistory.filter(record => record.category.includes('独角兽')).length;

            document.getElementById('total-evaluations').textContent = totalEvaluations;
            document.getElementById('eagle-count').textContent = eagleCount;
            document.getElementById('gazelle-count').textContent = gazelleCount;
            document.getElementById('unicorn-count').textContent = unicornCount;

            updateHistoryTable();
        }

        // 更新历史记录表格
        function updateHistoryTable() {
            const tbody = document.getElementById('history-tbody');

            if (evaluationHistory.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="padding: 20px; text-align: center; color: #666;">暂无评估记录</td></tr>';
                return;
            }

            tbody.innerHTML = '';
            evaluationHistory.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="padding: 12px; border: 1px solid #ddd;">${record.name}</td>
                    <td style="padding: 12px; border: 1px solid #ddd; text-align: center;">${record.category}</td>
                    <td style="padding: 12px; border: 1px solid #ddd; text-align: center;">${record.score}</td>
                    <td style="padding: 12px; border: 1px solid #ddd; text-align: center;">${record.timestamp}</td>
                    <td style="padding: 12px; border: 1px solid #ddd; text-align: center;">
                        <button onclick="viewDetails(${record.id})" style="padding: 4px 8px; margin-right: 5px; border: 1px solid #4facfe; background: white; color: #4facfe; border-radius: 4px; cursor: pointer;">详情</button>
                        <button onclick="deleteRecord(${record.id})" style="padding: 4px 8px; border: 1px solid #dc3545; background: white; color: #dc3545; border-radius: 4px; cursor: pointer;">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 查看详情
        function viewDetails(recordId) {
            const record = evaluationHistory.find(r => r.id === recordId);
            if (record) {
                alert(`企业详情：
企业名称：${record.name}
企业类型：${record.category}
综合得分：${record.score}
年营业收入：${record.data.revenue}万元
年增长率：${record.data.growth}%
研发投入占比：${record.data.rd}%
成立年限：${record.data.age}年
员工人数：${record.data.employees}人
企业估值：${record.data.valuation}亿元
技术创新指数：${record.data.innovation}
市场占有率：${record.data.market}%
评估时间：${record.timestamp}`);
            }
        }

        // 删除记录
        function deleteRecord(recordId) {
            if (confirm('确定要删除这条记录吗？')) {
                evaluationHistory = evaluationHistory.filter(r => r.id !== recordId);
                localStorage.setItem('evaluationHistory', JSON.stringify(evaluationHistory));
                updateAnalysisData();
            }
        }

        // 导出数据
        function exportData() {
            if (evaluationHistory.length === 0) {
                alert('暂无数据可导出！');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "企业名称,企业类型,综合得分,年营业收入(万元),年增长率(%),研发投入占比(%),成立年限(年),员工人数(人),企业估值(亿元),技术创新指数,市场占有率(%),评估时间\n"
                + evaluationHistory.map(record =>
                    `${record.name},${record.category},${record.score},${record.data.revenue},${record.data.growth},${record.data.rd},${record.data.age},${record.data.employees},${record.data.valuation},${record.data.innovation},${record.data.market},${record.timestamp}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "企业评估数据_" + new Date().toISOString().slice(0,10) + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 导出评审数据
        function exportReviewData() {
            if (applications.length === 0) {
                alert('暂无数据可导出！');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "企业名称,申报类型,机器得分,审核状态,申报时间,审核时间,审核意见\n"
                + applications.map(app =>
                    `${app.companyName},${app.type === 'eagle' ? '雏鹰企业' : app.type === 'gazelle' ? '瞪羚企业' : '独角兽企业'},${app.screening.score.toFixed(1)},${getStatusText(app.status)},${app.submitTime},${app.reviewTime || ''},${app.reviewComment || ''}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "企业申报评审数据_" + new Date().toISOString().slice(0,10) + ".csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 生成10条样例数据
        function generateSampleData() {
            const sampleCompanies = [
                {
                    companyName: "创新科技有限公司",
                    type: "eagle",
                    establishDate: "2022-03-15",
                    capital: "200",
                    employees: "25",
                    revenue: "800",
                    revenueGrowth: "45",
                    profitGrowth: "35",
                    rdRatio: "8",
                    patents: "3",
                    techRatio: "40",
                    description: "专注于人工智能技术研发的创新型企业，主要产品包括智能语音识别系统和机器学习平台"
                },
                {
                    companyName: "智能制造股份公司",
                    type: "gazelle",
                    establishDate: "2019-06-20",
                    capital: "1000",
                    employees: "120",
                    revenue: "5000",
                    revenueGrowth: "35",
                    profitGrowth: "28",
                    rdRatio: "6",
                    patents: "15",
                    techRatio: "35",
                    marketShare: "8",
                    description: "工业4.0智能制造解决方案提供商，为传统制造业提供数字化转型服务"
                },
                {
                    companyName: "未来科技独角兽",
                    type: "unicorn",
                    establishDate: "2018-01-10",
                    capital: "5000",
                    employees: "500",
                    revenue: "50000",
                    revenueGrowth: "80",
                    profitGrowth: "65",
                    valuation: "15",
                    rdRatio: "12",
                    patents: "50",
                    techRatio: "60",
                    marketShare: "15",
                    description: "新能源汽车自动驾驶技术领军企业，拥有完整的自动驾驶解决方案"
                },
                {
                    companyName: "绿色能源科技",
                    type: "eagle",
                    establishDate: "2023-01-08",
                    capital: "150",
                    employees: "18",
                    revenue: "600",
                    revenueGrowth: "55",
                    profitGrowth: "42",
                    rdRatio: "5",
                    patents: "2",
                    techRatio: "35",
                    description: "专注于太阳能发电技术和储能系统的研发，致力于清洁能源解决方案"
                },
                {
                    companyName: "数字医疗创新",
                    type: "gazelle",
                    establishDate: "2020-04-12",
                    capital: "800",
                    employees: "85",
                    revenue: "3200",
                    revenueGrowth: "42",
                    profitGrowth: "38",
                    rdRatio: "7",
                    patents: "12",
                    techRatio: "45",
                    marketShare: "6",
                    description: "医疗AI诊断系统开发商，产品覆盖影像诊断、病理分析等多个领域"
                },
                {
                    companyName: "云计算服务平台",
                    type: "unicorn",
                    establishDate: "2017-09-25",
                    capital: "8000",
                    employees: "800",
                    revenue: "80000",
                    revenueGrowth: "95",
                    profitGrowth: "78",
                    valuation: "25",
                    rdRatio: "15",
                    patents: "80",
                    techRatio: "70",
                    marketShare: "22",
                    description: "领先的云计算和大数据服务提供商，服务覆盖全球多个国家和地区"
                },
                {
                    companyName: "生物科技研发",
                    type: "eagle",
                    establishDate: "2022-11-30",
                    capital: "300",
                    employees: "32",
                    revenue: "1200",
                    revenueGrowth: "38",
                    profitGrowth: "25",
                    rdRatio: "12",
                    patents: "5",
                    techRatio: "50",
                    description: "专业从事基因检测和个性化医疗技术研发的生物科技公司"
                },
                {
                    companyName: "智慧物流科技",
                    type: "gazelle",
                    establishDate: "2018-12-05",
                    capital: "1500",
                    employees: "200",
                    revenue: "8000",
                    revenueGrowth: "48",
                    profitGrowth: "35",
                    rdRatio: "8",
                    patents: "25",
                    techRatio: "40",
                    marketShare: "12",
                    description: "智能物流解决方案提供商，拥有完整的仓储自动化和配送优化系统"
                },
                {
                    companyName: "区块链金融科技",
                    type: "eagle",
                    establishDate: "2023-05-18",
                    capital: "180",
                    employees: "22",
                    revenue: "450",
                    revenueGrowth: "28",
                    profitGrowth: "18",
                    rdRatio: "6",
                    patents: "1",
                    techRatio: "45",
                    description: "基于区块链技术的数字金融服务平台，提供去中心化金融解决方案"
                },
                {
                    companyName: "量子计算先锋",
                    type: "unicorn",
                    establishDate: "2016-03-22",
                    capital: "12000",
                    employees: "600",
                    revenue: "35000",
                    revenueGrowth: "120",
                    profitGrowth: "95",
                    valuation: "30",
                    rdRatio: "20",
                    patents: "120",
                    techRatio: "80",
                    marketShare: "18",
                    description: "全球领先的量子计算技术公司，在量子芯片和量子算法方面拥有核心技术优势"
                }
            ];

            const contacts = ["张经理", "李总监", "王主任", "陈经理", "刘总", "赵主管", "孙经理", "周总监", "吴主任", "郑经理"];
            const statuses = ['pending', 'pending', 'approved', 'rejected', 'reviewing', 'pending', 'approved', 'reviewing', 'pending', 'approved'];
            const reviewComments = [
                '', '',
                '符合瞪羚企业标准，各项指标优秀，建议通过',
                '研发投入占比偏低，不符合雏鹰企业要求',
                '需要进一步核实技术创新能力和市场占有率数据',
                '',
                '独角兽企业各项指标均达标，技术实力突出',
                '财务数据需要进一步验证，建议补充相关证明材料',
                '',
                '量子计算领域技术领先，符合独角兽企业标准'
            ];

            sampleCompanies.forEach((company, index) => {
                const applicationData = {
                    id: Date.now() + index,
                    ...company,
                    contact: contacts[index],
                    submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
                    status: statuses[index]
                };

                // 执行自动化初筛
                const screeningResult = automaticScreening(applicationData);
                applicationData.screening = screeningResult;
                applicationData.autoStatus = screeningResult.passed ? 'passed' : 'failed';

                if (applicationData.status !== 'pending') {
                    applicationData.reviewComment = reviewComments[index];
                    applicationData.reviewTime = new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN');
                }

                applications.push(applicationData);
            });

            localStorage.setItem('applications', JSON.stringify(applications));
        }

        // 重置配置函数
        function resetEagleConfig() {
            if (confirm('确定要重置雏鹰企业配置为默认值吗？')) {
                // 重置为默认值的逻辑
                alert('雏鹰企业配置已重置！');
            }
        }

        function resetGazelleConfig() {
            if (confirm('确定要重置瞪羚企业配置为默认值吗？')) {
                alert('瞪羚企业配置已重置！');
            }
        }

        function resetUnicornConfig() {
            if (confirm('确定要重置独角兽企业配置为默认值吗？')) {
                alert('独角兽企业配置已重置！');
            }
        }

        function resetAllConfiguration() {
            if (confirm('确定要重置所有配置为默认值吗？')) {
                localStorage.removeItem('enterpriseConfig');
                localStorage.removeItem('currentVersionInfo');
                location.reload();
            }
        }

        // 版本管理功能
        function updateCurrentVersionInfo() {
            if (document.getElementById('current-version-name')) {
                document.getElementById('current-version-name').textContent = currentVersionInfo.name;
                document.getElementById('current-version-time').textContent = currentVersionInfo.time || '系统默认';
                document.getElementById('current-version-desc').textContent = currentVersionInfo.description;
            }
        }

        function loadConfigTemplates() {
            const tbody = document.getElementById('template-tbody');
            const countElement = document.getElementById('template-count');
            if (!tbody) return;

            // 更新模板数量
            if (countElement) {
                countElement.textContent = configTemplates.length;
            }

            if (configTemplates.length === 0) {
                tbody.innerHTML = `<tr>
                    <td colspan="4" style="padding: 50px; text-align: center; color: #666;">
                        <div style="font-size: 3em; margin-bottom: 15px; opacity: 0.3;">📋</div>
                        <div style="font-size: 1.1em; margin-bottom: 10px;">暂无配置模板</div>
                        <div style="font-size: 0.9em; color: #999;">点击"新增模板"创建您的第一个配置模板</div>
                    </td>
                </tr>`;
                return;
            }

            tbody.innerHTML = '';
            configTemplates.forEach(template => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">
                        <div style="font-weight: 500;">${template.name}</div>
                        <div style="font-size: 12px; color: #666;">${template.description}</div>
                    </td>
                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <span class="status-badge ${template.active ? 'status-approved' : 'status-pending'}">
                            ${template.active ? '启用' : '停用'}
                        </span>
                    </td>
                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6; text-align: center; font-size: 13px; color: #666;">
                        ${template.createTime}
                    </td>
                    <td style="padding: 15px; border-bottom: 1px solid #dee2e6; text-align: center;">
                        <button onclick="editTemplate('${template.id}')" style="padding: 6px 12px; margin-right: 5px; border: 1px solid #667eea; background: white; color: #667eea; border-radius: 4px; cursor: pointer; font-size: 12px;">编辑</button>
                        <button onclick="applyTemplate('${template.id}')" style="padding: 6px 12px; margin-right: 5px; border: 1px solid #28a745; background: white; color: #28a745; border-radius: 4px; cursor: pointer; font-size: 12px;">应用</button>
                        <button onclick="toggleTemplate('${template.id}')" style="padding: 6px 12px; margin-right: 5px; border: 1px solid ${template.active ? '#dc3545' : '#ffc107'}; background: white; color: ${template.active ? '#dc3545' : '#ffc107'}; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            ${template.active ? '停用' : '启用'}
                        </button>
                        <button onclick="deleteTemplate('${template.id}')" style="padding: 6px 12px; border: 1px solid #dc3545; background: white; color: #dc3545; border-radius: 4px; cursor: pointer; font-size: 12px;">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }




        function applyTemplate(templateId) {
            const template = configTemplates.find(t => t.id === templateId);
            if (!template) return;

            if (confirm(`确定要应用模板"${template.name}"吗？这将覆盖当前配置。`)) {
                // 停用其他模板
                configTemplates.forEach(t => t.active = false);
                template.active = true;

                // 应用配置
                currentConfig = JSON.parse(JSON.stringify(template.config));

                // 更新当前版本信息
                currentVersionInfo = {
                    name: template.name,
                    time: template.createTime,
                    description: template.description
                };

                // 保存到本地存储
                localStorage.setItem('enterpriseConfig', JSON.stringify(currentConfig));
                localStorage.setItem('configTemplates', JSON.stringify(configTemplates));
                localStorage.setItem('currentVersionInfo', JSON.stringify(currentVersionInfo));

                // 刷新界面
                updateConfigUI();
                updateCurrentVersionInfo();
                loadConfigTemplates();

                alert('模板应用成功！');
            }
        }

        function toggleTemplate(templateId) {
            const template = configTemplates.find(t => t.id === templateId);
            if (!template) return;

            template.active = !template.active;

            // 如果启用此模板，停用其他模板
            if (template.active) {
                configTemplates.forEach(t => {
                    if (t.id !== templateId) t.active = false;
                });

                // 更新当前版本信息
                currentVersionInfo = {
                    name: template.name,
                    time: template.createTime,
                    description: template.description
                };
                localStorage.setItem('currentVersionInfo', JSON.stringify(currentVersionInfo));
                updateCurrentVersionInfo();
            }

            localStorage.setItem('configTemplates', JSON.stringify(configTemplates));
            loadConfigTemplates();
        }

        function deleteTemplate(templateId) {
            const template = configTemplates.find(t => t.id === templateId);
            if (!template) return;

            if (confirm(`确定要删除模板"${template.name}"吗？此操作不可恢复。`)) {
                configTemplates = configTemplates.filter(t => t.id !== templateId);
                localStorage.setItem('configTemplates', JSON.stringify(configTemplates));
                loadConfigTemplates();
                alert('模板删除成功！');
            }
        }

        // 临时清除数据功能（用于测试）
        function clearAllData() {
            if (confirm('确定要清除所有数据吗？这将删除所有申报记录和配置模板。')) {
                localStorage.removeItem('applications');
                localStorage.removeItem('configTemplates');
                localStorage.removeItem('currentVersionInfo');
                localStorage.removeItem('enterpriseConfig');
                alert('数据已清除，页面将刷新');
                location.reload();
            }
        }
    </script>
</body>
</html>
